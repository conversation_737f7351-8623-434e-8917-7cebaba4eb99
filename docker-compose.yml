version: '3.8'

services:
  tdload:
    build: .
    container_name: tdload
    restart: unless-stopped
    environment:
      # Telegram Bot Configuration
      - BOT_TOKEN=${BOT_TOKEN}
      - ADMIN_USER_ID=${ADMIN_USER_ID}

      # WebDAV Configuration
      - WEBDAV_HOSTNAME=${WEBDAV_HOSTNAME}
      - WEBDAV_LOGIN=${WEBDAV_LOGIN}
      - WEBDAV_PASSWORD=${WEBDAV_PASSWORD}
      - WEBDAV_PATH=${WEBDAV_PATH:-/}
    volumes:
      # Persist authorized users data
      - ./data:/app/data
    networks:
      - tdload-network

networks:
  tdload-network:
    driver: bridge
