#!/bin/bash

# TDLoad - Quick Start Example
# 这是一个示例启动脚本，请根据你的实际配置修改环境变量

echo "TDLoad - Telegram WebDAV Bot Quick Start"
echo "========================================"
echo ""
echo "请修改以下环境变量为你的实际配置："
echo ""

# 设置你的环境变量
export BOT_TOKEN="YOUR_TELEGRAM_BOT_TOKEN_HERE"
export ADMIN_USER_ID="YOUR_TELEGRAM_USER_ID_HERE"
export WEBDAV_HOSTNAME="https://your-webdav-server.com"
export WEBDAV_LOGIN="your_webdav_username"
export WEBDAV_PASSWORD="your_webdav_password"
export WEBDAV_PATH="/path/to/save/"

# 检查是否已修改配置
if [ "$BOT_TOKEN" = "YOUR_TELEGRAM_BOT_TOKEN_HERE" ]; then
    echo "❌ 请先修改此脚本中的环境变量配置！"
    echo ""
    echo "需要修改的变量："
    echo "- BOT_TOKEN: 你的Telegram机器人Token"
    echo "- ADMIN_USER_ID: 你的Telegram用户ID"
    echo "- WEBDAV_HOSTNAME: WebDAV服务器地址"
    echo "- WEBDAV_LOGIN: WebDAV用户名"
    echo "- WEBDAV_PASSWORD: WebDAV密码"
    echo "- WEBDAV_PATH: WebDAV保存路径"
    exit 1
fi

echo "✅ 配置已设置，开始部署..."
echo ""

# 使用Docker直接运行
docker run -d \
  --name tdload \
  --restart unless-stopped \
  -e BOT_TOKEN="$BOT_TOKEN" \
  -e ADMIN_USER_ID="$ADMIN_USER_ID" \
  -e WEBDAV_HOSTNAME="$WEBDAV_HOSTNAME" \
  -e WEBDAV_LOGIN="$WEBDAV_LOGIN" \
  -e WEBDAV_PASSWORD="$WEBDAV_PASSWORD" \
  -e WEBDAV_PATH="$WEBDAV_PATH" \
  -v $(pwd)/data:/app/data \
  your-dockerhub-username/tdload:latest

echo "🚀 TDLoad 已启动！"
echo ""
echo "查看日志: docker logs -f tdload"
echo "停止服务: docker stop tdload"
echo "删除容器: docker rm tdload"
