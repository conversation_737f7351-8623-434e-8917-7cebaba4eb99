import os
import json
import logging
from typing import Set
from telegram import Update
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, CommandHandler, MessageHandler, filters, ContextTypes
from webdav3.client import Client
import io

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# --- Configuration from Environment Variables ---
BOT_TOKEN = os.getenv("BOT_TOKEN")
ADMIN_USER_ID = int(os.getenv("ADMIN_USER_ID", "0"))
WEBDAV_HOSTNAME = os.getenv("WEBDAV_HOSTNAME")
WEBDAV_LOGIN = os.getenv("WEBDAV_LOGIN")
WEBDAV_PASSWORD = os.getenv("WEBDAV_PASSWORD")
WEBDAV_PATH = os.getenv("WEBDAV_PATH", "/")

# User management
DATA_DIR = os.getenv("DATA_DIR", "/app/data")
AUTHORIZED_USERS_FILE = os.path.join(DATA_DIR, "authorized_users.json")
authorized_users: Set[int] = set()

# Ensure data directory exists
os.makedirs(DATA_DIR, exist_ok=True)

def load_authorized_users():
    """Load authorized users from file"""
    global authorized_users
    try:
        if os.path.exists(AUTHORIZED_USERS_FILE):
            with open(AUTHORIZED_USERS_FILE, 'r') as f:
                data = json.load(f)
                authorized_users = set(data.get('users', []))
                logger.info(f"Loaded {len(authorized_users)} authorized users")
        else:
            authorized_users = {ADMIN_USER_ID} if ADMIN_USER_ID else set()
            save_authorized_users()
    except Exception as e:
        logger.error(f"Error loading authorized users: {e}")
        authorized_users = {ADMIN_USER_ID} if ADMIN_USER_ID else set()

def save_authorized_users():
    """Save authorized users to file"""
    try:
        with open(AUTHORIZED_USERS_FILE, 'w') as f:
            json.dump({'users': list(authorized_users)}, f)
        logger.info(f"Saved {len(authorized_users)} authorized users")
    except Exception as e:
        logger.error(f"Error saving authorized users: {e}")

def is_admin(user_id: int) -> bool:
    """Check if user is admin"""
    return user_id == ADMIN_USER_ID

def is_authorized(user_id: int) -> bool:
    """Check if user is authorized"""
    return user_id in authorized_users or is_admin(user_id)

def is_private_chat(update: Update) -> bool:
    """Check if message is from private chat"""
    return update.message.chat.type == 'private'

async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle /start command"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "User"

    if not is_private_chat(update):
        await update.message.reply_text("❌ 此机器人只能在私聊中使用。")
        return

    welcome_msg = f"👋 欢迎 {user_name}!\n\n"
    welcome_msg += f"🆔 您的用户ID: `{user_id}`\n\n"

    if is_authorized(user_id):
        welcome_msg += "✅ 您已被授权使用此机器人的文件上传功能。\n\n"
        welcome_msg += "📁 发送文件、图片或视频，我会帮您上传到WebDAV服务器。\n\n"
        if is_admin(user_id):
            welcome_msg += "👑 管理员命令:\n"
            welcome_msg += "/add_user <user_id> - 添加授权用户\n"
            welcome_msg += "/remove_user <user_id> - 移除授权用户\n"
            welcome_msg += "/list_users - 查看授权用户列表\n"
    else:
        welcome_msg += "❌ 您暂未被授权使用文件上传功能。\n"
        welcome_msg += "请联系管理员添加您的用户ID到授权列表。"

    await update.message.reply_text(welcome_msg, parse_mode='Markdown')

async def get_id_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle /id command - available to everyone"""
    if not is_private_chat(update):
        await update.message.reply_text("❌ 此机器人只能在私聊中使用。")
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "User"
    await update.message.reply_text(
        f"👤 {user_name}\n🆔 您的用户ID: `{user_id}`",
        parse_mode='Markdown'
    )

async def add_user_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle /add_user command - admin only"""
    if not is_private_chat(update):
        await update.message.reply_text("❌ 此机器人只能在私聊中使用。")
        return

    user_id = update.effective_user.id

    if not is_admin(user_id):
        await update.message.reply_text("❌ 只有管理员可以使用此命令。")
        return

    if not context.args:
        await update.message.reply_text("❌ 请提供用户ID。\n用法: /add_user <user_id>")
        return

    try:
        target_user_id = int(context.args[0])
        authorized_users.add(target_user_id)
        save_authorized_users()
        await update.message.reply_text(f"✅ 用户 {target_user_id} 已添加到授权列表。")
    except ValueError:
        await update.message.reply_text("❌ 无效的用户ID。")
    except Exception as e:
        logger.error(f"Error adding user: {e}")
        await update.message.reply_text("❌ 添加用户时发生错误。")

async def remove_user_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle /remove_user command - admin only"""
    if not is_private_chat(update):
        await update.message.reply_text("❌ 此机器人只能在私聊中使用。")
        return

    user_id = update.effective_user.id

    if not is_admin(user_id):
        await update.message.reply_text("❌ 只有管理员可以使用此命令。")
        return

    if not context.args:
        await update.message.reply_text("❌ 请提供用户ID。\n用法: /remove_user <user_id>")
        return

    try:
        target_user_id = int(context.args[0])
        if target_user_id == ADMIN_USER_ID:
            await update.message.reply_text("❌ 不能移除管理员。")
            return

        if target_user_id in authorized_users:
            authorized_users.remove(target_user_id)
            save_authorized_users()
            await update.message.reply_text(f"✅ 用户 {target_user_id} 已从授权列表中移除。")
        else:
            await update.message.reply_text(f"❌ 用户 {target_user_id} 不在授权列表中。")
    except ValueError:
        await update.message.reply_text("❌ 无效的用户ID。")
    except Exception as e:
        logger.error(f"Error removing user: {e}")
        await update.message.reply_text("❌ 移除用户时发生错误。")

async def list_users_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle /list_users command - admin only"""
    if not is_private_chat(update):
        await update.message.reply_text("❌ 此机器人只能在私聊中使用。")
        return

    user_id = update.effective_user.id

    if not is_admin(user_id):
        await update.message.reply_text("❌ 只有管理员可以使用此命令。")
        return

    if not authorized_users:
        await update.message.reply_text("📝 授权用户列表为空。")
        return

    user_list = "\n".join([f"• {uid}" for uid in sorted(authorized_users)])
    await update.message.reply_text(f"📝 授权用户列表 ({len(authorized_users)} 个用户):\n\n{user_list}")

async def handle_file(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle file uploads"""
    if not is_private_chat(update):
        await update.message.reply_text("❌ 此机器人只能在私聊中使用。")
        return

    user_id = update.effective_user.id

    if not is_authorized(user_id):
        await update.message.reply_text(
            "❌ 您没有权限上传文件。\n"
            f"🆔 您的用户ID: `{user_id}`\n"
            "请联系管理员添加您到授权列表。",
            parse_mode='Markdown'
        )
        return

    message = update.message
    file_id = None
    file_name = None

    if message.document:
        file_id = message.document.file_id
        file_name = message.document.file_name
    elif message.video:
        file_id = message.video.file_id
        file_name = message.video.file_name or f"{file_id}.mp4"
    elif message.photo:
        file_id = message.photo[-1].file_id
        file_name = f"{file_id}.jpg"
    elif message.audio:
        file_id = message.audio.file_id
        file_name = message.audio.file_name or f"{file_id}.mp3"
    elif message.voice:
        file_id = message.voice.file_id
        file_name = f"{file_id}.ogg"

    if not file_id or not file_name:
        await message.reply_text("❌ 不支持的文件类型。")
        return

    await message.reply_text(f"📁 收到文件: {file_name}\n⏳ 开始上传到WebDAV...")

    try:
        # Check WebDAV configuration
        if not all([WEBDAV_HOSTNAME, WEBDAV_LOGIN, WEBDAV_PASSWORD]):
            await message.reply_text("❌ WebDAV配置不完整，请联系管理员。")
            return

        bot = context.bot
        new_file = await bot.get_file(file_id)

        # Use an in-memory byte stream
        with io.BytesIO() as file_stream:
            await new_file.download_to_memory(out=file_stream)
            file_stream.seek(0)

            # Connect to WebDAV and upload the stream
            webdav_options = {
                'webdav_hostname': WEBDAV_HOSTNAME,
                'webdav_login': WEBDAV_LOGIN,
                'webdav_password': WEBDAV_PASSWORD
            }

            client = Client(webdav_options)
            remote_path = f"{WEBDAV_PATH.rstrip('/')}/{file_name}"
            client.upload_fileobj(file_stream, remote_path)

        await message.reply_text(f"✅ 文件 {file_name} 已成功上传到WebDAV。")
        logger.info(f"User {user_id} uploaded file: {file_name}")

    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        await message.reply_text("❌ 上传过程中发生错误，请稍后重试。")

async def handle_unauthorized_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle messages from unauthorized users or non-private chats"""
    if not is_private_chat(update):
        return  # Ignore group messages silently

    user_id = update.effective_user.id
    if not is_authorized(user_id):
        await update.message.reply_text(
            f"❌ 您没有权限使用此机器人。\n"
            f"🆔 您的用户ID: `{user_id}`\n"
            "请联系管理员添加您到授权列表。\n\n"
            "您可以使用 /id 命令查看您的用户ID。",
            parse_mode='Markdown'
        )

def main() -> None:
    """Main function"""
    if not BOT_TOKEN:
        logger.error("BOT_TOKEN environment variable is required")
        return

    if not ADMIN_USER_ID:
        logger.error("ADMIN_USER_ID environment variable is required")
        return

    # Load authorized users
    load_authorized_users()

    # Create application
    application = ApplicationBuilder().token(BOT_TOKEN).build()

    # Add command handlers
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("id", get_id_command))
    application.add_handler(CommandHandler("add_user", add_user_command))
    application.add_handler(CommandHandler("remove_user", remove_user_command))
    application.add_handler(CommandHandler("list_users", list_users_command))

    # Add file handlers for authorized users only
    file_filter = (filters.Document.ALL | filters.VIDEO | filters.PHOTO |
                   filters.AUDIO | filters.VOICE) & filters.ChatType.PRIVATE
    application.add_handler(MessageHandler(file_filter, handle_file))

    # Handle other messages
    application.add_handler(MessageHandler(filters.ALL, handle_unauthorized_message))

    logger.info("Bot started successfully")
    application.run_polling()

if __name__ == '__main__':
    main()