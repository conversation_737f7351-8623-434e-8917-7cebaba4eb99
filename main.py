from telegram import Update
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, Command<PERSON><PERSON><PERSON>, MessageHandler, filters
from webdav3.client import Client
import io

# --- WebDAV Configuration ---
WEBDAV_OPTIONS = {
    'webdav_hostname': "https://your-webdav-server.com",
    'webdav_login': "your_username",
    'webdav_password': "your_password"
}
WEBDAV_PATH = "/path/to/save/"

async def handle_file(update: Update, context) -> None:
    message = update.message
    file_id = None
    file_name = None

    if message.document:
        file_id = message.document.file_id
        file_name = message.document.file_name
    elif message.video:
        file_id = message.video.file_id
        file_name = message.video.file_name
    elif message.photo:
        # Handle photos (usually sent as compressed images)
        file_id = message.photo[-1].file_id # Get the highest resolution
        file_name = f"{file_id}.jpg" # You might want a better naming scheme

    if file_id and file_name:
        await message.reply_text(f"Received {file_name}. Starting upload to WebDAV...")
        try:
            bot = context.bot
            new_file = await bot.get_file(file_id)

            # Use an in-memory byte stream
            with io.BytesIO() as file_stream:
                await new_file.download_to_memory(out=file_stream)
                file_stream.seek(0) # Go to the beginning of the stream

                # Connect to WebDAV and upload the stream
                client = Client(WEBDAV_OPTIONS)
                remote_path = f"{WEBDAV_PATH}{file_name}"
                client.upload_fileobj(file_stream, remote_path)

            await message.reply_text(f"Successfully uploaded {file_name} to WebDAV.")
        except Exception as e:
            print(f"Error: {e}")
            await message.reply_text("An error occurred during the upload.")

def main() -> None:
    application = ApplicationBuilder().token("YOUR_TELEGRAM_BOT_TOKEN").build()

    application.add_handler(MessageHandler(filters.Document.ALL | filters.VIDEO | filters.PHOTO, handle_file))

    application.run_polling()

if __name__ == '__main__':
    main()