# TDLoad - Telegram WebDAV Bot

一个功能强大的Telegram机器人，支持将文件上传到WebDAV服务器，具有用户管理和权限控制功能。

## 功能特性

- 🔐 **用户权限管理**: 管理员可以控制哪些用户可以使用机器人
- 📁 **文件上传**: 支持上传文档、图片、视频、音频等多种文件类型到WebDAV服务器
- 🔒 **私聊限制**: 机器人只在私聊中工作，确保安全性
- 🆔 **用户ID查询**: 所有用户都可以查询自己的Telegram用户ID
- 🐳 **Docker部署**: 完整的Docker化部署方案
- 🚀 **CI/CD**: 自动化构建和部署到DockerHub

## 快速开始

### 1. 准备工作

1. **创建Telegram机器人**：
   - 与 @BotFather 对话创建机器人
   - 获取Bot Token

2. **获取你的Telegram用户ID**：
   - 与 @userinfobot 对话获取你的用户ID
   - 或者先运行机器人，使用 `/id` 命令查看

3. **准备WebDAV服务器信息**：
   - WebDAV服务器地址
   - 用户名和密码
   - 保存路径

### 2. 使用Docker Compose部署

1. 克隆项目：
```bash
git clone <your-repo-url>
cd tdload
```

2. 设置环境变量并启动服务：
```bash
export BOT_TOKEN="your_telegram_bot_token_here"
export ADMIN_USER_ID="your_telegram_user_id_here"
export WEBDAV_HOSTNAME="https://your-webdav-server.com"
export WEBDAV_LOGIN="your_webdav_username"
export WEBDAV_PASSWORD="your_webdav_password"
export WEBDAV_PATH="/path/to/save/"

docker-compose up -d
```

### 3. 使用Docker直接部署（推荐）

```bash
# 创建数据目录
mkdir -p data

# 运行容器
docker run -d \
  --name tdload \
  --restart unless-stopped \
  -e BOT_TOKEN="your_bot_token" \
  -e ADMIN_USER_ID="your_user_id" \
  -e WEBDAV_HOSTNAME="https://your-webdav-server.com" \
  -e WEBDAV_LOGIN="your_username" \
  -e WEBDAV_PASSWORD="your_password" \
  -e WEBDAV_PATH="/path/to/save/" \
  -v $(pwd)/data:/app/data \
  your-dockerhub-username/tdload:latest
```

### 4. 使用示例脚本快速部署

1. 下载并编辑示例脚本：
```bash
wget https://raw.githubusercontent.com/your-username/tdload/main/start-example.sh
chmod +x start-example.sh
# 编辑脚本中的环境变量
nano start-example.sh
```

2. 运行脚本：
```bash
./start-example.sh
```

## 环境变量说明

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `BOT_TOKEN` | ✅ | Telegram机器人Token |
| `ADMIN_USER_ID` | ✅ | 管理员的Telegram用户ID |
| `WEBDAV_HOSTNAME` | ✅ | WebDAV服务器地址 |
| `WEBDAV_LOGIN` | ✅ | WebDAV用户名 |
| `WEBDAV_PASSWORD` | ✅ | WebDAV密码 |
| `WEBDAV_PATH` | ❌ | WebDAV保存路径（默认: /） |
| `DATA_DIR` | ❌ | 数据目录（默认: /app/data） |

## 机器人命令

### 普通用户命令
- `/start` - 开始使用机器人，查看状态和帮助信息
- `/id` - 查看自己的Telegram用户ID

### 管理员命令
- `/add_user <user_id>` - 添加用户到授权列表
- `/remove_user <user_id>` - 从授权列表中移除用户
- `/list_users` - 查看所有授权用户

## 使用说明

1. **获取用户ID**: 任何用户都可以通过`/id`命令查看自己的用户ID
2. **授权用户**: 管理员使用`/add_user`命令添加用户到授权列表
3. **上传文件**: 授权用户直接发送文件给机器人即可自动上传到WebDAV服务器
4. **支持的文件类型**: 文档、图片、视频、音频、语音消息

## 开发和构建

### 本地开发

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 设置环境变量并运行：
```bash
export BOT_TOKEN="your_bot_token"
export ADMIN_USER_ID="your_user_id"
export WEBDAV_HOSTNAME="https://your-webdav-server.com"
export WEBDAV_LOGIN="your_username"
export WEBDAV_PASSWORD="your_password"
export WEBDAV_PATH="/path/to/save/"
python main.py
```

### 构建Docker镜像

```bash
docker build -t tdload .
```

## GitHub Actions设置

要启用自动构建和推送到DockerHub，需要在GitHub仓库中设置以下Secrets：

- `DOCKERHUB_USERNAME`: DockerHub用户名
- `DOCKERHUB_TOKEN`: DockerHub访问令牌

## 安全注意事项

- 机器人只接受私聊消息，不会在群组中工作
- 只有授权用户才能上传文件
- 管理员权限不能被移除
- 所有敏感信息通过环境变量配置
- 使用非root用户运行容器

## 故障排除

### 常见问题

1. **机器人无响应**
   - 检查Bot Token是否正确
   - 确认网络连接正常

2. **上传失败**
   - 检查WebDAV服务器配置
   - 确认WebDAV路径存在且有写入权限

3. **权限问题**
   - 确认ADMIN_USER_ID设置正确
   - 检查用户是否在授权列表中

### 查看日志

```bash
# Docker Compose
docker-compose logs -f

# Docker
docker logs -f tdload
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
