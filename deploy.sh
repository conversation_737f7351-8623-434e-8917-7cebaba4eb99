#!/bin/bash

# TDLoad - Telegram WebDAV Bot Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Docker and Docker Compose are installed."
}

# Check if required environment variables are set
check_env_vars() {
    local missing_vars=()

    if [ -z "$BOT_TOKEN" ]; then
        missing_vars+=("BOT_TOKEN")
    fi

    if [ -z "$ADMIN_USER_ID" ]; then
        missing_vars+=("ADMIN_USER_ID")
    fi

    if [ -z "$WEBDAV_HOSTNAME" ]; then
        missing_vars+=("WEBDAV_HOSTNAME")
    fi

    if [ -z "$WEBDAV_LOGIN" ]; then
        missing_vars+=("WEBDAV_LOGIN")
    fi

    if [ -z "$WEBDAV_PASSWORD" ]; then
        missing_vars+=("WEBDAV_PASSWORD")
    fi

    if [ ${#missing_vars[@]} -ne 0 ]; then
        print_error "The following required environment variables are not set:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        echo ""
        print_error "Please set these environment variables before running the script."
        echo "Example:"
        echo "  export BOT_TOKEN=\"your_bot_token\""
        echo "  export ADMIN_USER_ID=\"your_user_id\""
        echo "  export WEBDAV_HOSTNAME=\"https://your-webdav-server.com\""
        echo "  export WEBDAV_LOGIN=\"your_username\""
        echo "  export WEBDAV_PASSWORD=\"your_password\""
        echo "  export WEBDAV_PATH=\"/path/to/save/\""
        exit 1
    fi

    print_status "All required environment variables are set."
}

# Create data directory
create_data_dir() {
    if [ ! -d "data" ]; then
        mkdir -p data
        print_status "Created data directory."
    fi
}

# Deploy the bot
deploy() {
    print_status "Starting deployment..."
    
    # Stop existing containers
    docker-compose down
    
    # Pull latest images (if using pre-built image)
    # docker-compose pull
    
    # Build and start containers
    docker-compose up -d --build
    
    print_status "Deployment completed successfully!"
    print_status "Bot is running in the background."
    
    # Show logs
    print_status "Showing recent logs (press Ctrl+C to exit):"
    docker-compose logs -f
}

# Update the bot
update() {
    print_status "Updating the bot..."
    
    # Pull latest code (if using git)
    if [ -d ".git" ]; then
        git pull
    fi
    
    # Rebuild and restart
    docker-compose down
    docker-compose up -d --build
    
    print_status "Update completed successfully!"
}

# Show status
status() {
    print_status "Bot status:"
    docker-compose ps
    
    print_status "Recent logs:"
    docker-compose logs --tail=20
}

# Stop the bot
stop() {
    print_status "Stopping the bot..."
    docker-compose down
    print_status "Bot stopped."
}

# Show help
show_help() {
    echo "TDLoad - Telegram WebDAV Bot Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  deploy    Deploy the bot (default)"
    echo "  update    Update and restart the bot"
    echo "  status    Show bot status and logs"
    echo "  stop      Stop the bot"
    echo "  help      Show this help message"
    echo ""
    echo "Before first deployment, set environment variables:"
    echo "  export BOT_TOKEN=\"your_bot_token\""
    echo "  export ADMIN_USER_ID=\"your_user_id\""
    echo "  export WEBDAV_HOSTNAME=\"https://your-webdav-server.com\""
    echo "  export WEBDAV_LOGIN=\"your_username\""
    echo "  export WEBDAV_PASSWORD=\"your_password\""
    echo "  export WEBDAV_PATH=\"/path/to/save/\""
    echo ""
    echo "Then run: $0 deploy"
}

# Main script
main() {
    case "${1:-deploy}" in
        deploy)
            check_docker
            check_env_vars
            create_data_dir
            deploy
            ;;
        update)
            check_docker
            update
            ;;
        status)
            status
            ;;
        stop)
            stop
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
