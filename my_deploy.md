### 3. 使用Docker直接部署（推荐）

```bash
# 创建数据目录
mkdir -p /root/tdload/data

# 运行容器
docker run -d \
  --name tdload \
  --restart unless-stopped \
  -e BOT_TOKEN="8446926020:AAEDZzAB9ljvn1BckkRt3x4Af3qkDg_ShMY" \
  -e ADMIN_USER_ID="6923564798" \
  -e WEBDAV_HOSTNAME="https://alist.xrsite.online" \
  -e WEBDAV_LOGIN="jhxxr" \
  -e WEBDAV_PASSWORD="jhx666666" \
  -e WEBDAV_PATH="/dav/Crypt/Crypt移动/see/tbot" \
  -v /root/tdload/data:/app/data \
  jhxxr/tdload:latest
```